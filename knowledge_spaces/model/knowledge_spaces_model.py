from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field
from sqlalchemy import Index
from multi_agent.model.ai_application_model import db


class KnowledgeSpaceModel(db.Model):
    """知识空间表SQLAlchemy模型"""
    __tablename__ = 'knowledge_spaces'
    
    id = db.Column(db.<PERSON>Integer, primary_key=True, autoincrement=True, comment='知识空间ID')
    user_id = db.Column(db.BigInteger, nullable=False, comment='所属用户ID（所有者）')
    parent_id = db.Column(db.BigInteger, nullable=True, comment='父级知识空间ID (0表示顶级空间)')
    name = db.Column(db.String(100), nullable=False, comment='知识空间名称')
    description = db.Column(db.String(300), nullable=True, comment='知识空间描述')
    level = db.Column(db.<PERSON>Integer, nullable=False, default=0, comment='层级深度 (0=顶级, 1=一级子空间, 2=二级子空间...)')
    path = db.Column(db.String(255), nullable=False, default='', comment='空间路径 (如 "1,5,12" 表示ID为12的空间，其父是5，祖先是1)')
    creator = db.Column(db.BigInteger, nullable=False, comment='创建人ID')
    create_time = db.Column(db.DateTime, default=datetime.now, nullable=False, comment='创建时间')
    updater = db.Column(db.BigInteger, nullable=False, comment='更新人ID')
    update_time = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now, nullable=False, comment='更新时间')
    status = db.Column(db.SmallInteger, default=1, nullable=False, comment='状态 (1=启用, 0=禁用)')
    deleted = db.Column(db.Boolean, default=False, nullable=False, comment='是否删除')
    
    # 创建索引
    __table_args__ = (
        Index('idx_user_id', 'user_id'),
        Index('idx_parent_id', 'parent_id'),
        Index('idx_path', 'path'),
        Index('idx_status_deleted', 'status', 'deleted'),
    )
    
    def to_dict(self):
        """Convert model to dictionary with camelCase keys"""
        return {
            'id': self.id,
            'userId': self.user_id,
            'parentId': self.parent_id,
            'name': self.name,
            'description': self.description,
            'level': self.level,
            'path': self.path,
            'creator': self.creator,
            'createTime': self.create_time.isoformat() if self.create_time else None,
            'updater': self.updater,
            'updateTime': self.update_time.isoformat() if self.update_time else None,
            'status': self.status,
            'deleted': self.deleted,
        }


# DO (Data Object) - 数据传输对象，用于API请求/响应
class KnowledgeSpaceDO(BaseModel):
    """知识空间数据对象 - 统一的数据模型"""
    
    userId: Optional[int] = Field(None, alias='user_id', description='所属用户ID')
    parentId: Optional[int] = Field(None, alias='parent_id', description='父级知识空间ID')
    name: Optional[str] = Field(None, max_length=100, description='知识空间名称')
    description: Optional[str] = Field(None, max_length=300, description='知识空间描述')
    level: Optional[int] = Field(0, description='层级深度')
    path: Optional[str] = Field('', max_length=255, description='空间路径')
    creator: Optional[int] = Field(None, description='创建人ID')
    updater: Optional[int] = Field(None, description='更新人ID')
    status: Optional[int] = Field(1, description='状态 (1=启用, 0=禁用)')
    
    class Config:
        from_attributes = True
        populate_by_name = True


# VO (View Object) - 视图对象，用于响应展示
class KnowledgeSpaceVO(KnowledgeSpaceDO):
    """知识空间视图对象 - 包含展示相关字段"""
    
    id: Optional[int] = Field(None, description='知识空间ID')
    createTime: Optional[datetime] = Field(None, alias='create_time', description='创建时间')
    updateTime: Optional[datetime] = Field(None, alias='update_time', description='更新时间')
    deleted: Optional[bool] = Field(False, description='是否删除')
    
    # 扩展字段
    children: Optional[List['KnowledgeSpaceVO']] = Field([], description='子空间列表')
    childrenCount: Optional[int] = Field(0, description='子空间数量')


# 创建请求对象
class KnowledgeSpaceCreateRequest(BaseModel):
    """创建知识空间请求对象"""
    
    userId: int = Field(..., description='所属用户ID')
    parentId: Optional[int] = Field(None, description='父级知识空间ID')
    name: str = Field(..., min_length=1, max_length=100, description='知识空间名称')
    description: Optional[str] = Field(None, max_length=300, description='知识空间描述')
    creator: int = Field(..., description='创建人ID')


# 更新请求对象
class KnowledgeSpaceUpdateRequest(BaseModel):
    """更新知识空间请求对象"""

    userId: int = Field(..., description='用户ID (用于权限验证)')
    name: Optional[str] = Field(None, min_length=1, max_length=100, description='知识空间名称')
    description: Optional[str] = Field(None, max_length=300, description='知识空间描述')
    status: Optional[int] = Field(None, description='状态 (1=启用, 0=禁用)')
    updater: int = Field(..., description='更新人ID')


# 查询请求对象
class KnowledgeSpaceQueryRequest(BaseModel):
    """查询知识空间请求对象"""
    
    userId: Optional[int] = Field(None, description='所属用户ID')
    parentId: Optional[int] = Field(None, description='父级知识空间ID')
    name: Optional[str] = Field(None, description='名称模糊搜索')
    level: Optional[int] = Field(None, description='层级深度')
    status: Optional[int] = Field(None, description='状态')
    includeChildren: Optional[bool] = Field(False, description='是否包含子空间信息')
    
    # 分页参数
    page: Optional[int] = Field(1, ge=1, description='页码')
    pageSize: Optional[int] = Field(20, ge=1, le=100, description='每页大小')
    
    # 排序参数
    orderBy: Optional[str] = Field('create_time', description='排序字段')
    orderDesc: Optional[bool] = Field(True, description='是否倒序')


# 移动空间请求对象
class KnowledgeSpaceMoveRequest(BaseModel):
    """移动知识空间请求对象"""

    userId: int = Field(..., description='用户ID (用于权限验证)')
    targetParentId: Optional[int] = Field(None, description='目标父空间ID')
    updater: int = Field(..., description='更新人ID')